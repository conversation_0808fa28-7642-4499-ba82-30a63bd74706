import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.jsx";

window.onload = function (e) {
    const element = document.createElement("div");

    element.setAttribute("id", "ca-ai-assist");

    document.body.appendChild(element);

    createRoot(element).render(
        <StrictMode>
            <App />
        </StrictMode>
    );
};
