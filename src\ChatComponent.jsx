import user from "./assets/user.jpg";
import classNames from "classnames";
import Loading from "./AiLoading/Loading";
import {mdRender} from './utils/markdown'
export const ChatComponent = ({ chatsList, avatar }) => {
  const md = mdRender()
  return (
    <>
      {chatsList.map((item, index) => (
        <div
          className={classNames(
            "flex gap-1.5 max-w-full mb-4 w-[100%]",
            item.role == "user" ? "justify-end" : "justify-start"
          )}
          key={index}
        >
          {item.role == "user" ? (
            <>
              <div className="flex flex-col items-end user">
                <div className="py-3.5 px-5 rounded-2xl rounded-tr-none bg-blue-50 break-all dark:bg-[#121212] dark:text-[#bdbdbd]">
                  {item.content}
                </div>
              </div>
              <div>
                <div className="md:w-12 md:h-12 w-10 h-10 rounded-[3rem] overflow-hidden">
                  <img src={user}></img>
                </div>
              </div>
            </>
          ) : (
            <>
              <div>
                <div className="md:w-12 md:h-12 w-10 h-10 rounded-[3rem] overflow-hidden">
                  <img src={avatar}></img>
                </div>
              </div>
              <div className=" flex flex-col">
                <div className="py-3.5 px-5 rounded-2xl rounded-tl-none bg-white dark:bg-[#121212] dark:text-[#bdbdbd]">
                  {item.loading ? (
                    <Loading />
                  ) : (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: md.render(item.content),
                      }}
                    />
                  )}
                  {/* {md.render(item.content)} */}
                </div>
              </div>
            </>
          )}
        </div>
      ))}
    </>
  );
};
