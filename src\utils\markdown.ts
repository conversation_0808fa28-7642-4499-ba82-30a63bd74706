import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';
import MarkdownIt from 'markdown-it';
export const mdRender = () => {
  const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    highlight: (str: string, lang: string) => {
      if (lang && hljs.getLanguage(lang)) {
        try {
          // 使用 highlight.js 进行语法高亮
          const highlightedCode = hljs.highlight(str, { language: lang }).value;
          return `
					<div class="code-block-container">
						<div class="code" >
							<div class="code-header " >
								<small class="">${lang}</small>
							</div>
							<div class='sticky'>
								<div class='codeCopy '>
                    复制
								</div>
							</div>
						<pre class="hljs code-body language-${lang}" >${highlightedCode}</pre>
						</div>
					</div>`;
        } catch (__) {}
      }
      // 默认使用，未提供语言的情况下
      const defaultCode = md.utils.escapeHtml(str);
      return `
				<div class="code-block-container">
						<div class="code" >
							<div class="code-header " >
								<small class="">code</small>
							</div>
							<div class='sticky'>
								<div class='codeCopy '>
                    复制
								</div>
							</div>
						<pre class="hljs code-body language-${lang}" >${defaultCode}</pre>
						</div>
					</div>`;
    },
  });
  md.use((md: any) => {
    // 覆盖表格的渲染规则
    md.renderer.rules.table_open = function () {
      // 将表格包裹在 div 中，并添加 class 到 table
      return `<div class="table-overflow"><div class="table-responsive  mb-3"><div class='downloadTable'></div><table class="table table-bordered text-nowrap mb-0">`;
    };
    // 确保表格闭合标签
    md.renderer.rules.table_close = function () {
      return '</table></div></div>';
    };
    
  });
  return md;
};
