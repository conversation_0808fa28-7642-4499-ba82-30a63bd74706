@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  overflow: hidden;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}

button:hover {
  border-color: #646cff;
}

button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

/* 通用列表样式 */
ul, ol {
  counter-reset: list-counter; /* 初始化计数器 */
  list-style: none; /* 移除默认的列表标记 */
  padding-left: 0; /* 移除默认的缩进 */
  margin-top: 0; /* 统一顶部间距 */
  margin-bottom: 1em; /* 统一底部间距 */
}

/* 通用列表项样式 */
li {
  margin-bottom: 10px; /* 添加列表项之间的间距 */
  margin-inline: 20px 0; /* 水平方向的间距 */
  padding-inline: 4px 0; /* 水平方向的内边距 */
}

/* 有序列表的样式 */
ol li {
  counter-increment: list-counter; /* 每个 li 增加计数器 */
  position: relative; /* 为伪元素提供定位上下文 */
  padding-left: 20px; /* 添加缩进 */
}

ol li::before {
  content: counter(list-counter) ". "; /* 使用计数器生成序号 */
  font-weight: bold; /* 序号加粗 */
  position: absolute;
  left: 0; /* 将序号定位到左侧 */
  color: #333; /* 序号颜色 */
  font-size: 1em; /* 序号大小 */
  margin-right: 5px; /* 序号和文本之间的间距 */
}

/* 无序列表的样式 */
ul li {
  counter-increment: list-counter; /* 每个 li 增加计数器 */
  position: relative; /* 为伪元素提供定位上下文 */
  padding-left: 20px; /* 添加缩进 */
}

ul li::before {
  content: "•"; /* 使用 Unicode 字符表示小圆点 */
  position: absolute;
  left: 0; /* 将小圆点定位到左侧 */
  color: #333; /* 小圆点颜色 */
  font-size: 1em; /* 小圆点大小 */
}



@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }

  a:hover {
    color: #747bff;
  }

  button {
    background-color: #f9f9f9;
  }
}