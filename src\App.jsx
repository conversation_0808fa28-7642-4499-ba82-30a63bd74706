import { useState, useRef, useEffect } from "react";
import robot from "./assets/robot.png";
import robotIcon from "./assets/ai_m.png";
import HeaderComponent from "./HeaderComponent";
import "./App.css";
import { ChatComponent } from "./ChatComponent";
import classNames from "classnames";
import PaperPlane from "./assets/paperPlane.svg";
import axios from "axios";
const BASE_API = "https://dev.api.ai-desk.corporate-advisory.cn";
// const BASE_API = "https://api.testai.corporate-advisory.cn/train";
const roleId = "74575b1b-4b3c-44fe-9db3-062c19b4c2b4";
// const roleId = "69f77e7b-53a2-4964-96b8-cbd15904b3e7";

const postList = [];
let isDragging = false;
function App() {
  const [chatsList, setChatsList] = useState([]);
  const [chatShow, setChatShow] = useState(false);
  const [value, setValue] = useState("");
  const [rows, setRows] = useState(1);
  const [answerStatus, setAnswerStatus] = useState(false);
  const [fullScreen, setFullScreen] = useState(false);
  const [position, setPosition] = useState({ x: "50%", y: 0 });
  const modal = !!new URLSearchParams(window.location.search).get("modal");
  const [contextId, setContextId] = useState("");
  const [aiInfo, setAiInfo] = useState({
    roleName: "AI智能客服",
    roleDescription: "",
    roleGreeting: "您好，很高兴为您服务，请问有什么能帮到您？",
    roleLogo: robot,
  });
  const chatWindowRef = useRef(null);
  const draggableChatRef = useRef(null);
  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault(); // 防止默认的换行行为
      // setLoading(true);
      sendMessage();
    }
  };

  const sendAskContentApi = async (question) => {
    let aiBubble = {
      content: "",
      role: "ai",
      loading: true,
    };
    setChatsList((pre) => [...pre, aiBubble]);
    axios
      .post(
        // BASE_API + "/api/v1/assistant/gslChat",
        BASE_API + "/api/v1/ai/weChat",
        // BASE_API + "/api/v1/assistant/chat",
        { question, context_id: contextId },
        {
          timeout: 10000,
          onUploadProgress: () => {
            setAnswerStatus(true);
          },
        }
      )
      .then(function ({ data }) {
        if (data.status) {
          // aiBubble = { ...aiBubble, content: data.data, loading: false };
          setChatsList((pre) => {
            pre.at(-1).content = data.data;
            pre.at(-1).loading = false;
            localStorage.setItem("aiAssistChatList", JSON.stringify(pre));
            return pre;
          });
        }
        postList.shift();
        setAnswerStatus(false);
        if (postList.length > 0) {
          handlePostList();
        }
      })
      .catch(function (error) {
        console.log(error);
        if (error.code === "ERR_NETWORK" || error.code === "ECONNABORTED") {
          setChatsList((pre) => {
            const updatedChats = [...pre]; // 克隆原数组
            updatedChats[updatedChats.length - 1] = {
              ...updatedChats[updatedChats.length - 1],
              content: "网络不稳定，请稍后再试。",
              loading: false,
            };
            localStorage.setItem(
              "aiAssistChatList",
              JSON.stringify(updatedChats)
            );
            return updatedChats;
          });
        }
        postList.shift();
        setAnswerStatus(false);
        if (postList.length > 0) {
          handlePostList();
        }
      });
  };

  const handlePostList = (chatObj) => {
    if (postList.length > 0) {
      let obj = postList[0];
      sendAskContentApi(obj.content);
    }
  };

  //发送消息
  const sendMessage = async () => {
    setRows(1);
    let userAskBubble = {
      content: value.trim(),
      role: "user",
    };
    // if (userAskBubble.content.length === 0) {
    //   setValue("");
    //   return;
    // }
    if (value.length === 0) {
      setValue("");
      return;
    }
    if (chatsList.at(-1).loading) {
      await setChatsList((pre) => {
        let list = [...pre];
        let obj = list.pop();
        list.push(userAskBubble, obj);
        // localStorage.setItem('aiAssistChatList',JSON.stringify(list))
        return list;
      });
    } else {
      localStorage.setItem(
        "aiAssistChatList",
        JSON.stringify([...chatsList, userAskBubble])
      );
      await setChatsList((pre) => [...pre, userAskBubble]);
    }
    let chatObj = {
      // messages: [...chatsList, userAskBubble],
      roleId,
      content: value.trim(),
    };

    postList.push(chatObj);
    if (!answerStatus) {
      handlePostList();
    }
    setValue("");
  };

  const getAiInfo = () => {
    axios
      .get(`${BASE_API}/api/v1/ai/getContextId`, {
        params: {
          question: "你好",
        },
      })
      .then(function ({ data }) {
        if (data.status) {
          setContextId(data.data.context_id);
          // setAiInfo(data.data);
          // setChatsList([{ role: "ai", content: data.data.roleGreeting }]);
          setChatsList([
            {
              role: "ai",
              content:
                "您好！欢迎来到陈玮伦合伙人企业咨询AI客服系统，很高兴为您服务！请问有什么可以帮助您的",
            },
          ]);
        }
      })
      .catch(function (error) {
        console.log(error);
      });
  };
  const handleLimit = (target, type, width, height) => {
    let position = 0;
    if (type === "x" && target) {
      position = Math.min(Math.max(target, -300), width + 100);
    } else if (type === "y" && target) {
      position = Math.min(Math.max(target, 0), height - 100);
    }
    return position;
  };
  const handleMoveChat = (e, type) => {
    if (fullScreen && window.innerWidth >= 1280) {
      isDragging = true;
      let target = type === "mouseControl" ? e : e.touches[0];
      let eventMove = type === "mouseControl" ? "mousemove" : "touchmove";
      let eventUp = type === "mouseControl" ? "mouseup" : "touchend";

      const offsetX = target.clientX - draggableChatRef.current.offsetLeft;
      const offsetY = target.clientY - draggableChatRef.current.offsetTop;

      const handleMouseMove = (e) => {
        if (isDragging) {
          let currentTarget = type === "mouseControl" ? e : e.touches[0];
          let newX = currentTarget.clientX - offsetX;
          let newY = currentTarget.clientY - offsetY;

          // 获取屏幕宽度和高度
          const screenWidth = window.innerWidth;
          const screenHeight = window.innerHeight;

          // 限制 x 和 y 的值
          newX = handleLimit(newX, "x", screenWidth, screenHeight);
          newY = handleLimit(newY, "y", screenWidth, screenHeight);

          setPosition({
            x: newX + "px",
            y: newY + "px",
          });
        }
      };

      const handleMouseUp = () => {
        isDragging = false;
        document.removeEventListener(eventMove, handleMouseMove);
        document.removeEventListener(eventUp, handleMouseUp);
      };

      document.addEventListener(eventMove, handleMouseMove);
      document.addEventListener(eventUp, handleMouseUp);
    }
  };
  useEffect(() => {
    let element = chatWindowRef.current;
    if (element) {
      // 滚动到最后一条用户消息
      const userlist = element.querySelectorAll(".user");
      if (userlist.length > 0) {
        const lastUser = userlist[userlist.length - 1];
        element.scrollTop = lastUser.offsetTop - element.clientHeight + 78;
      }
      // 滚动到最底部
      // if (element.scrollHeight > element.clientHeight) {
      //   element.scrollTop = element.scrollHeight;
      // }
    }
  }, [chatsList, chatShow]);

  useEffect(() => {
    setPosition({ x: "50%", y: 0 });
  }, [fullScreen, chatShow]);

  useEffect(() => {
    let localList = JSON.parse(localStorage.getItem("aiAssistChatList"));
    if (localList && localList.length > 1) {
      localList = localList.filter((item) => !item.loading);
      setChatsList([...localList]);
      // if(localList.at(-1).role === 'user'){
      // }
    } else {
      getAiInfo();
    }
    if (modal) {
      setChatShow(true);
    }
  }, []);

  const clearChat = () => {
    localStorage.removeItem("aiAssistChatList");
    setChatsList((pre) => {
      let obj = pre[0];
      return [obj];
    });
  };

  return (
    <>
      <div
        ref={draggableChatRef}
        className={classNames(
          "z-999 fixed h-dvh flex chatMedia md:h-dvh", // md:absolute
          !chatShow && "hidden",
          fullScreen
            ? "xl:transform xl:-translate-x-1/2 md:w-full reset"
            : "right-0 top-0",
          !!modal && "w-full"
        )}
        style={
          fullScreen
            ? {
                left: `${position.x}`,
                top: `${position.y}`,
              }
            : {}
        }
      >
        <div
          className={classNames(
            "h-full flex flex-col md:rounded-2xl bg-[#060d3d] m-auto",
            modal
              ? "w-full md:h-full justify-end md:rounded-none"
              : fullScreen
              ? `xl:w-[900px] xl:h-[80vh] md:rounded-none xl:rounded-2xl`
              : "md:w-[380px] md:h-[80vh]"
          )}
        >
          <div
            onMouseDown={(e) => {
              handleMoveChat(e, "mouseControl");
            }}
            onTouchStart={(e) => {
              handleMoveChat(e, "touchControl");
            }}
            className={classNames(
              "md:h-14 h-[60px] w-full",
              fullScreen && "xl:cursor-move select-none"
            )}
          >
            <HeaderComponent
              text={answerStatus ? "对方正在输入中" : "在线"}
              setChatShow={() => {
                setChatShow(false);
              }}
              roleName={aiInfo.roleName}
              setFullScreen={(data) => setFullScreen(data)}
              fullScreen={fullScreen}
              clearChat={clearChat}
              modal={modal}
            />
          </div>
          <div
            className={classNames(
              " flex-1 bg-white dark:bg-[#121212] md:rounded-b-[1rem] text-black h-full flex flex-col justify-between  shadow-[0px_8px_32px_-12px_rgba(0,_0,_0,_0.41)] overflow-hidden md:h-[90vh] ",
              modal && "md:rounded-b-none",
              fullScreen && "md:rounded-b-none  xl:rounded-b-[1rem]"
            )}
            style={{ fontFamily: "math" }}
          >
            <div
              ref={chatWindowRef}
              className="scrollbar_thin md:h-[92%] bg-[#f6f6f6]  h-full md:p-5 py-5 px-4 overflow-auto dark:bg-[#181818] "
            >
              <ChatComponent
                chatsList={chatsList}
                avatar={aiInfo.roleLogo}
              ></ChatComponent>
            </div>
            <div className="md:h-[28%] md:content-between  md:py-5 md:px-6 py-5 px-4 border-t border-[#f0f0f0] bg-white flex  md:flex-col flex-row  gap-2.5 h-auto dark:bg-[#121212] dark:border-[#252525]">
              <textarea
                value={value}
                onChange={(e) => {
                  setValue(e.currentTarget.value);
                  const newRows = Math.min(
                    Math.floor(e.currentTarget.scrollHeight / 24),
                    6
                  );
                  setRows(newRows);
                }}
                className="dark:caret-white scrollbar_thin bg-white md:h-full w-full text-sm border-none focus:outline-none resize-none md:m-0 dark:bg-[#121212] dark:text-[#bdbdbd]"
                // max-rows="5"
                rows={rows}
                placeholder="请在此处输入您需要咨询的问题哦~"
                onKeyDown={(e) => handleKeyDown(e)}
              ></textarea>
              <div className="flex justify-end ">
                <button
                  onClick={sendMessage}
                  className="bg-[#060d3d] md:w-[90px] md:h-[37px] w-[30px] h-[30px]  text-white flex items-center rounded md:gap-2 md:px-3 md:py-2 text-sm p-0 p-1"
                >
                  <img className="w-[20px] h-[20px]" src={PaperPlane}></img>
                  <span className="md:inline hidden">发送</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        className={classNames(
          "fixed -bottom-2 md:right-6 md:w-36 md:h-36 md:-bottom-6 cursor-pointer w-24 h-24 right-2",
          chatShow && "hidden"
        )}
        onClick={() => {
          setChatShow(true);
        }}
      >
        <img src={robotIcon}></img>
        <span className="relative text-white text-[6px] bottom-[26px] left-[15%] md:text-[10px] md:bottom-[30px]">
          {aiInfo.roleName}
        </span>
      </div>
    </>
  );
}

export default App;
