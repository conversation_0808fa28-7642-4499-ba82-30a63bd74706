@keyframes loading{
    0% {
        transform: translateY(0);
    }
    10% {
        transform: translateY(4px);
    }
    20% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-4px);
    }
    40% {
        transform: translateY(0);
    }
}

.loadingDot{
    animation-name: loading;
    animation-duration: 2s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

.loadingDot:nth-child(1){
    animation-delay: 0s;
}
.loadingDot:nth-child(2){
    animation-delay: 0.2s;
}
.loadingDot:nth-child(3){
    animation-delay: 0.4s;
}