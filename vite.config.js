import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
import replace from "rollup-plugin-replace";

console.log(process.env.NODE_ENV);

const plugins = [react()];

if (process.env.NODE_ENV === "production") {
    plugins.push(
        replace({
            "process.env.NODE_ENV": JSON.stringify("production"),
        })
    );
}

// https://vite.dev/config/
export default defineConfig({
    plugins,
    build: {
        // 打包输出的目录
        outDir: "cna-ai-assist",
        // 防止 vite 将 rgba() 颜色转化为 #RGBA 十六进制
        cssTarget: "chrome61",
        lib: {
            // 组件库源码的入口文件
            entry: resolve("src/main.jsx"),
            // 组件库名称
            name: "CnaAiAssist",
            // 文件名称, 打包结果举例: my-packages.umd.cjs
            fileName: "index",
            formats: ["es", "umd", "cjs"],
        },
        rollupOptions: {
            output: {
                intro(chunkInfo) {
                    return `
                        var styleSheetLink = document.createElement('link');
                        styleSheetLink.setAttribute('rel', 'stylesheet');
                        styleSheetLink.setAttribute('href', '/cna-ai-assist/index.css');
                        document.head.appendChild(styleSheetLink);
                    `;
                },
            },
        },
    },
    server:{
      port:8080,
      host: '0.0.0.0', // 配置项目可以局域网访问
      cors: true, // 默认启用并允许任何源
    },
     base: './'
});
