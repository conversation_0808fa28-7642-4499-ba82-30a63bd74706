.box {
  height: 678px;
  width: 905px;
  border-radius: 1.5rem;
  background-color: rgb(6, 13, 61);
  margin: auto;
  overflow: hidden;
}

.chatTextArea:focus-visible {
  border: none;
}

.scrollbar_thin {
  scrollbar-width: thin;
  scrollbar-color: #060d3d transparent;
}

@media (max-width: 767px) {
  .chatMedia {
    /* 小于 md 的样式 */
    /* position: absolute; */
    left: 0px !important;
    top: 0px !important;
  }
}

@media (max-width: 1279px) {
  /* 小于 xl 的样式 */
  .reset {
    left: 0px !important;
    top: 0px !important;
  }
}
