name: React Deploy

on:
    push:
        branches:
            - dev

jobs:
    dev-deploy:
        if: github.ref == 'refs/heads/dev'

        runs-on: ubuntu-latest

        steps:
            - name: Checkout code
              uses: actions/checkout@v2

            - name: Install Node.js and pnpm
              run: |
                  sudo apt-get update
                  sudo apt-get install -y rsync sshpass curl
                  curl -fsSL https://get.pnpm.io/install.sh | SHELL=/bin/bash sh
                  echo "/home/<USER>/.local/share/pnpm" >> $GITHUB_PATH

            - name: Install dependencies
              run: |
                  export PATH="/home/<USER>/.local/share/pnpm:$PATH"
                  pnpm install --no-frozen-lockfile

            - name: Build project
              run: |
                  export PATH="/home/<USER>/.local/share/pnpm:$PATH"
                  pnpm run build

            - name: Deploy dev.cs.corporate-advisory.cn
              run: |
                  rsync -avz dist -e "sshpass -p nVDqS5dMcnVzdRmIb7pW ssh -o StrictHostKeyChecking=no" corporate-advisory-dev-cs@**************:/home/<USER>/htdocs/dev.cs.corporate-advisory.cn

